/**
 * GOAL TRACKER AGENT (META-AGENT)
 * 
 * This meta-agent provides conversation steering and goal tracking capabilities
 * to ensure consultations remain focused and achieve their intended objectives.
 * It operates behind the scenes to guide other agents toward goal completion.
 * 
 * FEATURES:
 * - Dynamic goal creation and tracking
 * - Conversation relevance scoring
 * - Real-time steering guidance for agents
 * - Progress monitoring and milestone tracking
 * - Adaptive conversation flow management
 * - Multi-goal prioritization and balancing
 */

import { BaseAgent } from './BaseAgent';
import { supabase } from '../utils/supabaseClient';
import { auditLogger } from '../utils/auditLogger';
import type { 
  IAgent, 
  AgentRequest, 
  AgentResponse, 
  AgentCapability,
  AgentRole
} from './BaseAgent';
import type { ConversationMessage } from '../services/MemoryManager';

interface SessionGoal {
  id: string;
  sessionId: string;
  goalType: string;
  primaryGoal: string;
  secondaryGoals: string[];
  priorityLevel: number;
  progressPercentage: number;
  completionCriteria: Record<string, any>;
  steeringNotes: string[];
}

interface SteeringGuidance {
  steeringType: 'redirect' | 'clarify' | 'deepen' | 'summarize' | 'conclude' | 'escalate' | 'refer' | 'educate' | 'reassure' | 'investigate';
  steeringMessage: string;
  relevanceScore: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  goalId: string;
}

interface GoalAnalysis {
  currentGoals: SessionGoal[];
  progressAssessment: Record<string, number>;
  steeringRecommendations: SteeringGuidance[];
  conversationRelevance: number;
  nextActions: string[];
}

export class GoalTrackerAgent extends BaseAgent {
  private conversationTurnCounter: Map<string, number> = new Map();
  private goalCache: Map<string, SessionGoal[]> = new Map();

  constructor(memoryManager: any) {
    const id = 'goal-tracker-001';
    const name = 'Goal Tracker';
    const role: AgentRole = 'meta_agent' as AgentRole;
    const capabilities: AgentCapability[] = [
      'goal_tracking',
      'conversation_steering',
      'progress_monitoring',
      'relevance_scoring'
    ] as AgentCapability[];

    const systemPrompt = `You are the Goal Tracker Meta-Agent, responsible for ensuring consultations achieve their intended objectives through intelligent conversation steering and progress monitoring.

CORE RESPONSIBILITIES:
- Analyze conversation flow for goal alignment
- Provide steering guidance to primary agents
- Track progress toward session objectives
- Identify when goals are being achieved or need redirection
- Maintain focus on patient priorities while ensuring comprehensive care

STEERING CAPABILITIES:
- Redirect: Guide conversation back to primary objectives
- Clarify: Request more specific information on key topics
- Deepen: Encourage more detailed exploration of important areas
- Summarize: Consolidate information and confirm understanding
- Conclude: Signal when goals have been sufficiently addressed
- Escalate: Identify when higher urgency or specialist care is needed
- Refer: Recommend specialist consultation for specific goals
- Educate: Suggest patient education opportunities
- Reassure: Provide emotional support when needed
- Investigate: Encourage further diagnostic exploration

ANALYSIS FRAMEWORK:
1. Goal Identification: Recognize explicit and implicit patient objectives
2. Progress Tracking: Monitor advancement toward each goal
3. Relevance Scoring: Assess how well conversation aligns with objectives
4. Steering Decisions: Determine when and how to guide conversation
5. Completion Assessment: Identify when goals have been adequately addressed

Remember: You operate behind the scenes to enhance the primary agent's effectiveness. Your guidance should be subtle and supportive, not disruptive to the patient experience.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager, []);
    console.log('🎯 Goal Tracker Agent initialized');
  }

  /**
   * Analyze conversation and provide goal tracking insights
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      console.log(`🎯 Goal Tracker analyzing session: ${request.sessionId}`);

      // Increment conversation turn counter
      const currentTurn = this.incrementTurnCounter(request.sessionId);

      // Get or create session goals
      const sessionGoals = await this.getOrCreateSessionGoals(request);

      // Analyze conversation for goal progress
      const goalAnalysis = await this.analyzeGoalProgress(request, sessionGoals, currentTurn);

      // Generate steering guidance if needed
      const steeringGuidance = await this.generateSteeringGuidance(
        request, 
        goalAnalysis, 
        currentTurn
      );

      // Update goal progress in database
      await this.updateGoalProgress(goalAnalysis, currentTurn);

      // Store steering guidance for primary agents
      if (steeringGuidance.length > 0) {
        await this.storeSteeringGuidance(request.sessionId, steeringGuidance, currentTurn);
      }

      const processingTime = Date.now() - startTime;

      return {
        agentId: this.id,
        response: this.formatGoalAnalysisResponse(goalAnalysis),
        confidence: 0.9, // High confidence in goal tracking
        handoffSuggestions: [],
        emergencyFlags: [],
        metadata: {
          processingTime,
          conversationTurn: currentTurn,
          activeGoals: sessionGoals.length,
          steeringGuidanceProvided: steeringGuidance.length,
          overallProgress: this.calculateOverallProgress(goalAnalysis)
        }
      };

    } catch (error) {
      console.error('❌ Goal Tracker Agent failed:', error);

      return {
        agentId: this.id,
        response: 'Goal tracking temporarily unavailable. Continuing with standard consultation flow.',
        confidence: 0.3,
        handoffSuggestions: [],
        emergencyFlags: [],
        metadata: {
          processingTime: Date.now() - startTime,
          error: error.message
        }
      };
    }
  }

  /**
   * Get or create session goals based on conversation context
   */
  private async getOrCreateSessionGoals(request: AgentRequest): Promise<SessionGoal[]> {
    try {
      // Check cache first
      const cachedGoals = this.goalCache.get(request.sessionId);
      if (cachedGoals) {
        return cachedGoals;
      }

      // Get existing goals from database
      const { data: existingGoals, error } = await supabase
        .rpc('get_active_session_goals', { p_session_id: request.sessionId });

      if (error) {
        console.warn('Failed to get session goals:', error);
        return [];
      }

      let sessionGoals: SessionGoal[] = existingGoals || [];

      // If no goals exist, create initial goals based on conversation
      if (sessionGoals.length === 0) {
        sessionGoals = await this.createInitialGoals(request);
      }

      // Cache the goals
      this.goalCache.set(request.sessionId, sessionGoals);

      return sessionGoals;

    } catch (error) {
      console.error('Failed to get/create session goals:', error);
      return [];
    }
  }

  /**
   * Create initial goals based on conversation context
   */
  private async createInitialGoals(request: AgentRequest): Promise<SessionGoal[]> {
    const goals: SessionGoal[] = [];

    try {
      // Analyze user message to identify primary concerns
      const primaryConcerns = this.identifyPrimaryConcerns(request.userMessage);
      
      for (const concern of primaryConcerns) {
        const { data: goalId } = await supabase
          .rpc('create_session_goal', {
            p_session_id: request.sessionId,
            p_user_id: request.patientContext?.userId,
            p_goal_type: concern.type,
            p_primary_goal: concern.description,
            p_secondary_goals: concern.secondaryGoals,
            p_priority_level: concern.priority,
            p_completion_criteria: concern.completionCriteria
          });

        if (goalId) {
          goals.push({
            id: goalId,
            sessionId: request.sessionId,
            goalType: concern.type,
            primaryGoal: concern.description,
            secondaryGoals: concern.secondaryGoals,
            priorityLevel: concern.priority,
            progressPercentage: 0,
            completionCriteria: concern.completionCriteria,
            steeringNotes: []
          });
        }
      }

      console.log(`✅ Created ${goals.length} initial goals for session`);

    } catch (error) {
      console.error('Failed to create initial goals:', error);
    }

    return goals;
  }

  /**
   * Identify primary concerns from user message
   */
  private identifyPrimaryConcerns(message: string): Array<{
    type: string;
    description: string;
    secondaryGoals: string[];
    priority: number;
    completionCriteria: Record<string, any>;
  }> {
    const concerns = [];
    const lowerMessage = message.toLowerCase();

    // Symptom assessment
    if (this.containsSymptoms(lowerMessage)) {
      concerns.push({
        type: 'symptom_assessment',
        description: 'Assess and understand reported symptoms',
        secondaryGoals: ['symptom_severity', 'symptom_duration', 'associated_symptoms'],
        priority: 1,
        completionCriteria: {
          symptom_details_gathered: false,
          severity_assessed: false,
          timeline_established: false
        }
      });
    }

    // Medication concerns
    if (lowerMessage.includes('medication') || lowerMessage.includes('medicine') || lowerMessage.includes('drug')) {
      concerns.push({
        type: 'medication_review',
        description: 'Review medication concerns and management',
        secondaryGoals: ['medication_effectiveness', 'side_effects', 'dosage_optimization'],
        priority: 2,
        completionCriteria: {
          current_medications_reviewed: false,
          concerns_addressed: false,
          recommendations_provided: false
        }
      });
    }

    // Preventive care
    if (lowerMessage.includes('checkup') || lowerMessage.includes('screening') || lowerMessage.includes('prevention')) {
      concerns.push({
        type: 'preventive_care',
        description: 'Provide preventive care guidance and screening recommendations',
        secondaryGoals: ['health_maintenance', 'risk_assessment', 'lifestyle_counseling'],
        priority: 3,
        completionCriteria: {
          risk_factors_assessed: false,
          screening_recommendations_provided: false,
          lifestyle_guidance_given: false
        }
      });
    }

    // Default general assessment if no specific concerns identified
    if (concerns.length === 0) {
      concerns.push({
        type: 'symptom_assessment',
        description: 'General health assessment and guidance',
        secondaryGoals: ['health_status_evaluation', 'patient_education'],
        priority: 2,
        completionCriteria: {
          assessment_completed: false,
          guidance_provided: false
        }
      });
    }

    return concerns;
  }

  /**
   * Check if message contains symptom-related content
   */
  private containsSymptoms(message: string): boolean {
    const symptomKeywords = [
      'pain', 'ache', 'hurt', 'sore', 'swelling', 'rash', 'fever',
      'headache', 'nausea', 'vomiting', 'diarrhea', 'constipation',
      'fatigue', 'tired', 'weakness', 'dizziness', 'shortness of breath',
      'cough', 'sore throat', 'runny nose', 'congestion', 'chest pain',
      'abdominal pain', 'back pain', 'joint pain', 'muscle pain'
    ];

    return symptomKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Analyze goal progress based on conversation
   */
  private async analyzeGoalProgress(
    request: AgentRequest, 
    sessionGoals: SessionGoal[], 
    currentTurn: number
  ): Promise<GoalAnalysis> {
    
    const progressAssessment: Record<string, number> = {};
    const steeringRecommendations: SteeringGuidance[] = [];
    const nextActions: string[] = [];

    // Analyze each goal
    for (const goal of sessionGoals) {
      const progress = this.assessGoalProgress(goal, request, currentTurn);
      progressAssessment[goal.id] = progress;

      // Generate steering recommendations based on progress
      if (progress < 30 && currentTurn > 3) {
        steeringRecommendations.push({
          steeringType: 'redirect',
          steeringMessage: `Focus more on addressing: ${goal.primaryGoal}`,
          relevanceScore: 0.8,
          urgencyLevel: 'medium',
          goalId: goal.id
        });
      } else if (progress > 80) {
        steeringRecommendations.push({
          steeringType: 'conclude',
          steeringMessage: `Goal "${goal.primaryGoal}" appears well-addressed. Consider summarizing and moving to next priority.`,
          relevanceScore: 0.9,
          urgencyLevel: 'low',
          goalId: goal.id
        });
      }
    }

    // Calculate overall conversation relevance
    const conversationRelevance = this.calculateConversationRelevance(request, sessionGoals);

    return {
      currentGoals: sessionGoals,
      progressAssessment,
      steeringRecommendations,
      conversationRelevance,
      nextActions
    };
  }

  /**
   * Assess progress for a specific goal
   */
  private assessGoalProgress(goal: SessionGoal, request: AgentRequest, currentTurn: number): number {
    // This would use more sophisticated analysis in production
    // For now, provide basic progress estimation based on conversation content
    
    let progress = goal.progressPercentage;
    const message = request.userMessage.toLowerCase();
    const goalKeywords = goal.primaryGoal.toLowerCase().split(' ');

    // Check if conversation is addressing this goal
    const relevantKeywords = goalKeywords.filter(keyword => message.includes(keyword));
    const relevanceRatio = relevantKeywords.length / goalKeywords.length;

    if (relevanceRatio > 0.5) {
      progress = Math.min(100, progress + 15); // Increment progress
    }

    return progress;
  }

  /**
   * Calculate overall conversation relevance to goals
   */
  private calculateConversationRelevance(request: AgentRequest, goals: SessionGoal[]): number {
    if (goals.length === 0) return 0.5; // Neutral relevance if no goals

    let totalRelevance = 0;
    const message = request.userMessage.toLowerCase();

    for (const goal of goals) {
      const goalKeywords = goal.primaryGoal.toLowerCase().split(' ');
      const matchingKeywords = goalKeywords.filter(keyword => message.includes(keyword));
      const goalRelevance = matchingKeywords.length / goalKeywords.length;
      totalRelevance += goalRelevance * (6 - goal.priorityLevel) / 5; // Weight by priority
    }

    return Math.min(1.0, totalRelevance / goals.length);
  }

  /**
   * Generate steering guidance for primary agents
   */
  private async generateSteeringGuidance(
    request: AgentRequest,
    analysis: GoalAnalysis,
    currentTurn: number
  ): Promise<SteeringGuidance[]> {
    
    const guidance: SteeringGuidance[] = [...analysis.steeringRecommendations];

    // Add relevance-based steering
    if (analysis.conversationRelevance < 0.4 && currentTurn > 2) {
      const highestPriorityGoal = analysis.currentGoals
        .sort((a, b) => a.priorityLevel - b.priorityLevel)[0];

      if (highestPriorityGoal) {
        guidance.push({
          steeringType: 'redirect',
          steeringMessage: `The conversation seems to be drifting from the main concern. Consider refocusing on: ${highestPriorityGoal.primaryGoal}`,
          relevanceScore: 0.9,
          urgencyLevel: 'medium',
          goalId: highestPriorityGoal.id
        });
      }
    }

    return guidance;
  }

  /**
   * Store steering guidance in database for primary agents
   */
  private async storeSteeringGuidance(
    sessionId: string,
    guidance: SteeringGuidance[],
    turnNumber: number
  ): Promise<void> {
    try {
      for (const guide of guidance) {
        await supabase.rpc('add_steering_guidance', {
          p_session_id: sessionId,
          p_goal_id: guide.goalId,
          p_turn_number: turnNumber,
          p_steering_type: guide.steeringType,
          p_steering_message: guide.steeringMessage,
          p_relevance_score: guide.relevanceScore,
          p_urgency_level: guide.urgencyLevel
        });
      }

      console.log(`✅ Stored ${guidance.length} steering guidance items`);

    } catch (error) {
      console.error('Failed to store steering guidance:', error);
    }
  }

  /**
   * Update goal progress in database
   */
  private async updateGoalProgress(analysis: GoalAnalysis, turnNumber: number): Promise<void> {
    try {
      for (const goal of analysis.currentGoals) {
        const currentProgress = analysis.progressAssessment[goal.id];
        const progressIncrement = currentProgress - goal.progressPercentage;

        if (progressIncrement > 0) {
          await supabase.rpc('update_goal_progress', {
            p_goal_id: goal.id,
            p_milestone: `Progress made in conversation turn ${turnNumber}`,
            p_progress_increment: progressIncrement,
            p_conversation_turn: turnNumber,
            p_agent_id: this.id,
            p_confidence_score: 0.8
          });
        }
      }

    } catch (error) {
      console.error('Failed to update goal progress:', error);
    }
  }

  /**
   * Format goal analysis response
   */
  private formatGoalAnalysisResponse(analysis: GoalAnalysis): string {
    const activeGoals = analysis.currentGoals.filter(g => g.progressPercentage < 100);
    const completedGoals = analysis.currentGoals.filter(g => g.progressPercentage >= 100);

    let response = `Goal Tracking Summary:\n`;
    response += `- Active Goals: ${activeGoals.length}\n`;
    response += `- Completed Goals: ${completedGoals.length}\n`;
    response += `- Conversation Relevance: ${(analysis.conversationRelevance * 100).toFixed(0)}%\n`;

    if (analysis.steeringRecommendations.length > 0) {
      response += `\nSteering Recommendations:\n`;
      analysis.steeringRecommendations.forEach(rec => {
        response += `- ${rec.steeringType.toUpperCase()}: ${rec.steeringMessage}\n`;
      });
    }

    return response;
  }

  /**
   * Calculate overall progress across all goals
   */
  private calculateOverallProgress(analysis: GoalAnalysis): number {
    if (analysis.currentGoals.length === 0) return 0;

    const totalProgress = Object.values(analysis.progressAssessment).reduce((sum, progress) => sum + progress, 0);
    return totalProgress / analysis.currentGoals.length;
  }

  /**
   * Increment and track conversation turn counter
   */
  private incrementTurnCounter(sessionId: string): number {
    const currentCount = this.conversationTurnCounter.get(sessionId) || 0;
    const newCount = currentCount + 1;
    this.conversationTurnCounter.set(sessionId, newCount);
    return newCount;
  }

  /**
   * Clean up session data to prevent memory leaks
   */
  public cleanupSession(sessionId: string): void {
    try {
      this.conversationTurnCounter.delete(sessionId);
      this.goalCache.delete(sessionId);
      console.log(`🧹 Cleaned up goal tracker data for session: ${sessionId}`);
    } catch (error) {
      console.warn('Failed to cleanup session data:', error);
    }
  }

  /**
   * Clean up old sessions (older than 24 hours)
   */
  public cleanupOldSessions(): void {
    try {
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
      let cleanedCount = 0;

      // This is a simple cleanup - in production, you'd track session timestamps
      // For now, we'll clean up sessions that haven't been accessed recently
      for (const [sessionId, goals] of this.goalCache.entries()) {
        const hasRecentActivity = goals.some(goal =>
          new Date(goal.updatedAt).getTime() > cutoffTime
        );

        if (!hasRecentActivity) {
          this.cleanupSession(sessionId);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} old sessions from goal tracker`);
      }
    } catch (error) {
      console.warn('Failed to cleanup old sessions:', error);
    }
  }

  /**
   * Get memory usage statistics
   */
  public getMemoryStats(): {
    activeSessions: number;
    totalGoals: number;
    memoryUsage: string;
  } {
    const activeSessions = this.conversationTurnCounter.size;
    const totalGoals = Array.from(this.goalCache.values()).reduce((sum, goals) => sum + goals.length, 0);

    // Rough memory usage calculation
    const memoryUsage = `${activeSessions} sessions, ${totalGoals} goals`;

    return {
      activeSessions,
      totalGoals,
      memoryUsage
    };
  }
}

// Export singleton instance
export const goalTrackerAgent = new GoalTrackerAgent(null);
