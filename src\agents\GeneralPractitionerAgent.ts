/**
 * GENERAL PRACTITIONER AGENT
 * 
 * Implements a comprehensive GP agent that provides primary care consultations.
 * Replaces the hardcoded GP prompt with a sophisticated agent that can:
 * - Assess general health concerns
 * - Provide primary care guidance
 * - Detect when specialist referral is needed
 * - Handle routine medical questions
 * - Manage chronic conditions
 * 
 * CAPABILITIES:
 * - Primary care assessment
 * - Preventive care guidance
 * - Chronic disease management
 * - Patient education
 * - Specialist referral decisions
 */

import { BaseAgent } from './BaseAgent';
import { RAGTool } from '../tools/RAGTool';
import { VisualAnalysisTool } from '../tools/VisualAnalysisTool';
import { diagnosticFrameworkService, type SOAPAssessment } from '../services/DiagnosticFrameworkService';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { contextAssemblyService } from '../services/ContextAssemblyService';
import { empathyMandateService } from '../services/EmpathyMandateService';
import type {
  AgentRequest,
  AgentResponse,
  Agent<PERSON>ole,
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class GeneralPractitionerAgent extends BaseAgent {
  private ragTool: RAGTool;
  private visualAnalysisTool: VisualAnalysisTool;
  private soapAssessments: Map<string, SOAPAssessment> = new Map(); // Track SOAP assessments by session

  constructor(memoryManager: MemoryManager) {
    const id = 'gp-agent-001';
    const name = 'Dr. Sarah Chen';
    const role: AgentRole = 'general_practitioner';
    const capabilities: AgentCapability[] = [
      'primary_care',
      'diagnostic_assessment',
      'treatment_planning',
      'patient_education',
      'preventive_care',
      'chronic_disease_management',
      'medication_management'
    ];

    // Initialize with RAG tool for knowledge retrieval and Visual Analysis tool for image analysis
    const tools = [new RAGTool(), new VisualAnalysisTool()];

    const systemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

AVAILABLE TOOLS:
- Medical Knowledge Retrieval (RAG): Access to current medical guidelines, research, and protocols
- Visual Analysis: Analyze medical images to provide diagnostic insights and recommendations

When providing consultations:
1. Use the RAG tool to retrieve relevant medical guidelines and research
2. If the patient has shared medical images, use the Visual Analysis tool to analyze them
3. Reference current evidence-based recommendations
4. Cite specific studies or guidelines when appropriate
5. Ensure recommendations align with latest clinical protocols
6. Proactively request images when visual assessment would be helpful for diagnosis

CORE RESPONSIBILITIES:
- Conduct thorough primary care assessments
- Provide evidence-based medical guidance
- Educate patients about their health conditions
- Identify when specialist referral is appropriate
- Manage chronic conditions and preventive care
- Ensure patient safety and appropriate care escalation

COMMUNICATION STYLE:
- Professional yet warm and empathetic
- Use clear, patient-friendly language
- Ask relevant follow-up questions for proper assessment
- Provide structured, actionable advice
- Always emphasize the importance of in-person medical care when appropriate

SAFETY PROTOCOLS:
- Immediately flag emergency situations requiring urgent care
- Recommend specialist consultation for complex conditions
- Never provide specific medication dosages without proper evaluation
- Always advise patients to seek immediate medical attention for serious symptoms
- Maintain clear boundaries of telemedicine limitations

SPECIALIZATION AREAS:
- General health assessments and wellness checks
- Common acute illnesses (colds, flu, minor infections)
- Chronic disease management (diabetes, hypertension, asthma)
- Preventive care and health screenings
- Mental health screening and basic counseling
- Women's health and family planning
- Geriatric care considerations
- Pediatric primary care (basic)

Remember: You are providing guidance and education, not replacing in-person medical examination and treatment. Always encourage patients to establish care with a local healthcare provider for ongoing medical needs.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager, tools);

    // Store tool references for easy access
    this.ragTool = tools[0] as RAGTool;
    this.visualAnalysisTool = tools[1] as VisualAnalysisTool;
  }

  /**
   * Handle incoming patient messages
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🩺 GP Agent processing request for session: ${request.sessionId}`);

      // Detect emergencies first
      const emergencyFlags = this.detectEmergencies(request.userMessage);
      
      // If critical emergency detected, prioritize emergency response
      if (emergencyFlags.some(flag => flag.severity === 'critical')) {
        return this.handleEmergencyResponse(request, emergencyFlags);
      }

      // Initialize or update SOAP assessment for structured consultation
      const soapAssessment = this.initializeOrUpdateSOAPAssessment(request);

      // Analyze the request for specialist referral needs
      const handoffSuggestions = this.analyzeForSpecialistReferral(request);

      // Generate GP response using structured diagnostic approach
      const response = await this.generateStructuredMedicalResponse(request, soapAssessment);

      // Calculate confidence based on request complexity
      const confidence = this.calculateConfidence(request);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, confidence, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      // Handle background image analysis completion (non-blocking)
      if (imageAnalysisPromise && !imageAnalysisResults) {
        imageAnalysisPromise
          .then(async (backgroundAnalysisResults) => {
            if (backgroundAnalysisResults?.success) {
              console.log('✅ Background image analysis completed, storing results');
              // Store analysis results for future reference
              await this.storeImageAnalysisResults(request.sessionId, backgroundAnalysisResults);
            }
          })
          .catch(error => {
            console.warn('Background image analysis failed:', error);
          });
      }

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence,
        reasoning: 'Primary care assessment completed with evidence-based guidance',
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: this.generateFollowUpActions(request),
        metadata: {
          responseTime,
          assessmentType: 'primary_care',
          specialistConsultationRecommended: handoffSuggestions.length > 0
        }
      };

    } catch (error) {
      console.error('❌ GP Agent error:', error);
      
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I apologize, but I'm experiencing some technical difficulties. For your safety, please consult with a healthcare provider directly if you have urgent medical concerns.",
        confidence: 0.1,
        reasoning: 'Technical error occurred during consultation',
        emergencyFlags: [{
          type: 'medical_emergency',
          severity: 'medium',
          description: 'System error - recommend direct medical consultation',
          recommendedAction: 'Consult healthcare provider directly',
          timeToResponse: 1500
        }]
      };
    }
  }

  /**
   * Handle emergency situations
   */
  private async handleEmergencyResponse(request: AgentRequest, emergencyFlags: EmergencyFlag[]): Promise<AgentResponse> {
    console.log('🚨 GP Agent handling emergency situation');

    const emergencyResponse = `I've detected that you may be experiencing a medical emergency. This requires immediate attention from emergency medical services.

IMMEDIATE ACTIONS:
1. If this is life-threatening, call emergency services (911/999/112) immediately
2. If you're experiencing chest pain, difficulty breathing, or severe symptoms, seek emergency care now
3. Contact your local emergency department or urgent care facility

I'm designed to provide general health guidance, but emergency situations require immediate professional medical intervention. Please don't delay in seeking appropriate emergency care.

If this is not an emergency, please rephrase your concern and I'll be happy to provide general health guidance.`;

    return {
      agentId: this.id,
      agentName: this.name,
      content: emergencyResponse,
      confidence: 0.95,
      reasoning: 'Emergency situation detected - immediate medical attention required',
      emergencyFlags,
      suggestedHandoffs: [{
        targetAgentRole: 'emergency',
        reason: 'Critical emergency situation detected',
        urgency: 'critical',
        contextToTransfer: `Emergency detected: ${emergencyFlags.map(f => f.description).join(', ')}`,
        patientConsent: false // Emergency override
      }],
      metadata: {
        emergencyDetected: true,
        responseTime: Date.now(),
        priorityLevel: 'critical'
      }
    };
  }

  /**
   * Analyze if specialist referral is needed
   */
  private analyzeForSpecialistReferral(request: AgentRequest): AgentHandoffSuggestion[] {
    const message = request.userMessage.toLowerCase();
    const suggestions: AgentHandoffSuggestion[] = [];

    // Cardiology referral indicators
    const cardioKeywords = ['heart', 'chest pain', 'palpitations', 'blood pressure', 'cardiac', 'cardiovascular'];
    if (cardioKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'cardiologist',
        reason: 'Cardiovascular symptoms detected - specialist consultation recommended',
        urgency: 'medium',
        contextToTransfer: 'Patient presenting with cardiovascular-related concerns'
      });
    }

    // Mental health referral indicators
    const mentalHealthKeywords = ['depression', 'anxiety', 'stress', 'mental health', 'mood', 'panic', 'therapy'];
    if (mentalHealthKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'psychiatrist',
        reason: 'Mental health concerns identified - specialist support recommended',
        urgency: 'medium',
        contextToTransfer: 'Patient expressing mental health concerns'
      });
    }

    // Nutrition referral indicators
    const nutritionKeywords = ['diet', 'weight', 'nutrition', 'eating', 'food', 'diabetes', 'cholesterol'];
    if (nutritionKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'nutritionist',
        reason: 'Nutritional concerns identified - dietary specialist consultation beneficial',
        urgency: 'low',
        contextToTransfer: 'Patient has nutrition-related questions or concerns'
      });
    }

    return suggestions;
  }

  /**
   * Initialize or update SOAP assessment for structured consultation
   */
  private initializeOrUpdateSOAPAssessment(request: AgentRequest): SOAPAssessment {
    let soapAssessment = this.soapAssessments.get(request.sessionId);

    if (!soapAssessment) {
      // Initialize new SOAP assessment
      const chiefComplaint = this.extractChiefComplaint(request.userMessage);
      soapAssessment = diagnosticFrameworkService.initializeSOAPAssessment(chiefComplaint);
      this.soapAssessments.set(request.sessionId, soapAssessment);
      console.log(`🩺 Initialized SOAP assessment for session: ${request.sessionId}`);
    } else {
      // Update existing assessment with new information
      soapAssessment = diagnosticFrameworkService.updateSOAPAssessment(
        soapAssessment,
        request.userMessage,
        this.categorizeInformation(request.userMessage)
      );
      this.soapAssessments.set(request.sessionId, soapAssessment);
      console.log(`🩺 Updated SOAP assessment: ${soapAssessment.completionPercentage}% complete`);
    }

    return soapAssessment;
  }

  /**
   * Extract chief complaint from user message
   */
  private extractChiefComplaint(message: string): string {
    // Simple extraction - in production, use NLP
    const symptoms = ['pain', 'ache', 'hurt', 'fever', 'cough', 'nausea', 'dizzy', 'tired', 'headache'];
    const lowerMessage = message.toLowerCase();

    for (const symptom of symptoms) {
      if (lowerMessage.includes(symptom)) {
        return `Patient reports ${symptom}`;
      }
    }

    return message.length > 100 ? message.substring(0, 100) + '...' : message;
  }

  /**
   * Categorize information type for SOAP update
   */
  private categorizeInformation(message: string): string {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('started') || lowerMessage.includes('began') || lowerMessage.includes('since')) {
      return 'hpi_onset';
    }
    if (lowerMessage.match(/\d+\/10|\d+ out of 10|scale of/)) {
      return 'hpi_severity';
    }
    if (lowerMessage.includes('medication') || lowerMessage.includes('taking') || lowerMessage.includes('pills')) {
      return 'medications';
    }
    if (lowerMessage.includes('allergic') || lowerMessage.includes('allergy')) {
      return 'allergies';
    }

    return 'general_information';
  }

  /**
   * Generate structured medical response using SOAP framework and comprehensive context
   */
  private async generateStructuredMedicalResponse(request: AgentRequest, soapAssessment: SOAPAssessment): Promise<string> {
    try {
      // Check if we have assembled context for personalized response
      if (request.assembledContext) {
        return await this.generateContextAwareStructuredResponse(request, soapAssessment);
      }

      // Fallback to basic structured response if no context available
      return await this.generateBasicStructuredResponse(request, soapAssessment);

    } catch (error) {
      console.error('❌ Failed to generate structured medical response:', error);
      return this.generateFallbackResponse();
    }
  }

  /**
   * Generate context-aware structured response using SOAP framework and LLM
   */
  private async generateContextAwareStructuredResponse(request: AgentRequest, soapAssessment: SOAPAssessment): Promise<string> {
    const { assembledContext } = request;
    const { contextBlock, priorityFlags, recommendations } = assembledContext;

    // Use RAG tool to get relevant medical knowledge with geographic context
    const patientContext = request.patientContext;
    const regionalContext = patientContext?.regionalContext;

    let ragResponse = null;
    try {
      ragResponse = await this.ragTool.execute({
        query: request.userMessage,
        parameters: {
          maxResults: 3,
          minRelevanceScore: 0.75,
          documentTypes: ['guideline', 'protocol'],
          specialtyFilter: 'general_medicine',
          regionFilter: regionalContext?.region,
          countryFilter: regionalContext?.countryCode,
          urgencyLevel: request.urgencyLevel || 'medium'
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: request.sessionId,
        agentId: this.id
      });
    } catch (error) {
      console.warn('RAG tool failed, continuing without knowledge retrieval:', error);
    }

    // Start image analysis in background if images are present (non-blocking)
    let imageAnalysisPromise: Promise<any> | null = null;
    let imageAnalysisResults = null;

    if (request.patientContext?.uploadedImages && request.patientContext.uploadedImages.length > 0) {
      console.log('🔍 Starting background image analysis...');

      const latestImage = request.patientContext.uploadedImages[request.patientContext.uploadedImages.length - 1];

      // Start image analysis in background
      imageAnalysisPromise = this.analyzeImageInBackground({
        imageUrl: latestImage.url,
        imageId: latestImage.id,
        sessionId: request.sessionId,
        analysisType: 'general',
        clinicalContext: request.userMessage,
        symptoms: this.extractSymptomsFromMessage(request.userMessage),
        urgencyLevel: request.urgencyLevel || 'medium'
      });

      // For emergency cases, wait for image analysis
      if (request.urgencyLevel === 'critical') {
        try {
          imageAnalysisResults = await imageAnalysisPromise;
          console.log('✅ Emergency image analysis completed:', imageAnalysisResults?.success);
        } catch (error) {
          console.warn('Emergency image analysis failed, continuing without visual insights:', error);
        }
      }
    }

    // Create simplified context for better LLM integration
    const simplifiedContext = await contextAssemblyService.assembleSimplifiedContext(
      patientContext,
      request.conversationHistory || [],
      request.userMessage,
      {
        maxTokens: 800,
        urgencyLevel: request.urgencyLevel,
        includeRegionalContext: true
      }
    );

    // Build enhanced system prompt with simplified context and emotional intelligence
    const enhancedSystemPrompt = this.buildEnhancedSystemPromptWithSimplifiedContext(
      simplifiedContext,
      soapAssessment,
      ragResponse,
      request.emotionalContext,
      imageAnalysisResults
    );

    // Create conversation messages with context-enhanced system prompt
    const messages = [
      {
        role: 'system',
        content: enhancedSystemPrompt
      },
      {
        role: 'user',
        content: request.userMessage
      }
    ];

    // Use AI orchestrator to generate context-aware response
    try {
      const aiResponse = await aiOrchestrator.generateResponse({
        messages,
        sessionId: request.sessionId,
        agentType: 'general-practitioner',
        maxTokens: 1000,
        temperature: 0.7,
        // Pass rich context to backend
        patientContext,
        assembledContext,
        regionalContext,
        medicalHistory: patientContext?.medicalHistory,
        urgencyLevel: request.urgencyLevel
      });

      if (aiResponse.success && aiResponse.data?.content) {
        return aiResponse.data.content;
      } else {
        console.warn('AI orchestrator failed, falling back to structured response');
        return this.buildFallbackStructuredResponse(request, soapAssessment, contextBlock);
      }
    } catch (error) {
      console.error('AI orchestrator error, falling back to structured response:', error);
      return this.buildFallbackStructuredResponse(request, soapAssessment, contextBlock);
    }
  }

  /**
   * Build enhanced system prompt with rich patient context
   */
  private buildEnhancedSystemPrompt(
    contextBlock: any,
    priorityFlags: string[],
    recommendations: string[],
    soapAssessment: SOAPAssessment,
    ragResponse: any
  ): string {
    let systemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

CURRENT PATIENT CONTEXT:
${contextBlock.patientProfile}

${contextBlock.medicalHistory}

${contextBlock.regionalContext}

CLINICAL GUIDANCE:
${contextBlock.clinicalGuidance}

EMERGENCY ALERTS:
${contextBlock.emergencyAlerts}

CULTURAL CONSIDERATIONS:
${contextBlock.culturalConsiderations}

SOAP ASSESSMENT STATUS:
- Current Phase: ${soapAssessment.currentPhase}
- Completion: ${soapAssessment.completionPercentage}%
- Key Findings: ${soapAssessment.keyFindings.join(', ') || 'None recorded'}

PRIORITY FLAGS:
${priorityFlags.join('\n')}

CONTEXTUAL RECOMMENDATIONS:
${recommendations.join('\n')}`;

    // Add RAG knowledge if available
    if (ragResponse?.success && ragResponse.data?.documents?.length > 0) {
      systemPrompt += `\n\nRELEVANT MEDICAL KNOWLEDGE:`;
      ragResponse.data.documents.forEach((doc: any, index: number) => {
        systemPrompt += `\n${index + 1}. ${doc.title} (Evidence Level: ${doc.evidenceLevel})
   ${doc.content.substring(0, 200)}...`;
      });
    }

    systemPrompt += `\n\nINSTRUCTIONS:
1. Provide personalized medical guidance based on the patient's profile and medical history
2. Consider regional health factors and cultural considerations in your response
3. Follow the SOAP framework for structured assessment
4. Reference relevant medical knowledge and evidence levels when appropriate
5. Maintain professional, compassionate communication
6. Clearly indicate when specialist referral or emergency care is needed
7. Provide specific, actionable recommendations based on the patient's context

Remember: This is a consultation with a real patient. Use their specific information to provide personalized, relevant medical guidance.`;

    return systemPrompt;
  }

  /**
   * Build enhanced system prompt with simplified context for better LLM integration
   */
  private buildEnhancedSystemPromptWithSimplifiedContext(
    simplifiedContext: any,
    soapAssessment: SOAPAssessment,
    ragResponse: any,
    emotionalContext?: import('../types/emotional').EmotionalContext,
    imageAnalysisResults?: any
  ): string {
    let systemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

PATIENT CONTEXT:
${simplifiedContext.contextSummary}

SOAP ASSESSMENT STATUS:
- Current Phase: ${soapAssessment.currentPhase}
- Completion: ${soapAssessment.completionPercentage}%
- Key Findings: ${soapAssessment.keyFindings.join(', ') || 'None recorded'}`;

    // Add priority alerts if any
    if (simplifiedContext.priorityAlerts.length > 0) {
      systemPrompt += `\n\nPRIORITY ALERTS:
${simplifiedContext.priorityAlerts.join('\n')}`;
    }

    // Add key recommendations if any
    if (simplifiedContext.keyRecommendations.length > 0) {
      systemPrompt += `\n\nKEY RECOMMENDATIONS:
${simplifiedContext.keyRecommendations.join('\n')}`;
    }

    // Add RAG knowledge if available
    if (ragResponse?.success && ragResponse.data?.documents?.length > 0) {
      systemPrompt += `\n\nRELEVANT MEDICAL KNOWLEDGE:`;
      ragResponse.data.documents.slice(0, 2).forEach((doc: any, index: number) => {
        systemPrompt += `\n${index + 1}. ${doc.title} (Evidence Level: ${doc.evidenceLevel})
   ${doc.content.substring(0, 150)}...`;
      });
    }

    systemPrompt += `\n\nINSTRUCTIONS:
1. Provide personalized medical guidance based on the patient's context
2. Follow the SOAP framework for structured assessment (Current Phase: ${soapAssessment.currentPhase})
3. Reference relevant medical knowledge when appropriate
4. Maintain professional, compassionate communication
5. Clearly indicate when specialist referral or emergency care is needed
6. Provide specific, actionable recommendations

Remember: This is a consultation with a real patient. Use their specific information to provide personalized, relevant medical guidance.`;

    // Add image analysis results if available
    if (imageAnalysisResults && imageAnalysisResults.success && imageAnalysisResults.data) {
      const analysis = imageAnalysisResults.data;
      systemPrompt += `\n\n=== MEDICAL IMAGE ANALYSIS RESULTS ===\n`;
      systemPrompt += `VISUAL FINDINGS:\n${analysis.findings.map(f => `- ${f}`).join('\n')}\n\n`;
      systemPrompt += `CLINICAL IMPRESSIONS:\n${analysis.impressions.map(i => `- ${i}`).join('\n')}\n\n`;
      systemPrompt += `RECOMMENDATIONS:\n${analysis.recommendations.map(r => `- ${r}`).join('\n')}\n\n`;
      systemPrompt += `URGENCY ASSESSMENT: ${analysis.urgencyAssessment.toUpperCase()}\n`;
      systemPrompt += `CONFIDENCE SCORE: ${(analysis.confidenceScore * 100).toFixed(0)}%\n`;

      if (analysis.requiresSpecialistReferral) {
        systemPrompt += `⚠️ SPECIALIST REFERRAL RECOMMENDED: ${analysis.specialistType}\n`;
      }

      if (analysis.emergencyFlags && analysis.emergencyFlags.length > 0) {
        systemPrompt += `🚨 EMERGENCY FLAGS: ${analysis.emergencyFlags.join(', ')}\n`;
      }

      systemPrompt += `\nIMPORTANT: Incorporate these visual findings into your clinical assessment and recommendations. Reference the image analysis when discussing the patient's condition.`;
    }

    // Add empathy mandate based on emotional context
    const empathyEnhancedPrompt = empathyMandateService.generateEmpathyEnhancedPrompt(
      systemPrompt,
      'general_practitioner',
      emotionalContext
    );

    return empathyEnhancedPrompt;
  }

  /**
   * Analyze image in background (non-blocking)
   */
  private async analyzeImageInBackground(analysisRequest: any): Promise<any> {
    try {
      return await this.visualAnalysisTool.execute(analysisRequest);
    } catch (error) {
      console.warn('Background image analysis failed:', error);
      return null;
    }
  }

  /**
   * Store image analysis results for future reference
   */
  private async storeImageAnalysisResults(sessionId: string, analysisResults: any): Promise<void> {
    try {
      // This would store the analysis results in the database
      // For now, just log the completion
      console.log(`📊 Stored image analysis results for session: ${sessionId}`);
    } catch (error) {
      console.warn('Failed to store image analysis results:', error);
    }
  }

  /**
   * Extract symptoms from user message for image analysis context
   */
  private extractSymptomsFromMessage(message: string): string[] {
    const symptoms: string[] = [];
    const commonSymptoms = [
      'pain', 'ache', 'swelling', 'rash', 'redness', 'itching', 'burning',
      'bleeding', 'discharge', 'fever', 'headache', 'nausea', 'vomiting',
      'diarrhea', 'constipation', 'fatigue', 'weakness', 'dizziness',
      'shortness of breath', 'cough', 'sore throat', 'runny nose',
      'congestion', 'chest pain', 'abdominal pain', 'back pain'
    ];

    const lowerMessage = message.toLowerCase();
    for (const symptom of commonSymptoms) {
      if (lowerMessage.includes(symptom)) {
        symptoms.push(symptom);
      }
    }

    return symptoms;
  }

  /**
   * Check if the patient's condition would benefit from visual assessment
   */
  private shouldRequestImage(message: string, soapAssessment: SOAPAssessment): boolean {
    const visualConditions = [
      'rash', 'skin', 'lesion', 'mole', 'bump', 'swelling', 'bruise',
      'cut', 'wound', 'sore', 'blister', 'redness', 'discoloration',
      'growth', 'lump', 'spot', 'mark', 'injury', 'burn'
    ];

    const lowerMessage = message.toLowerCase();
    return visualConditions.some(condition => lowerMessage.includes(condition));
  }

  /**
   * Build fallback structured response when AI orchestrator fails
   */
  private buildFallbackStructuredResponse(request: AgentRequest, soapAssessment: SOAPAssessment, contextBlock: any): string {
    const patientContext = request.patientContext;

    // Build personalized response using available context
    let response = this.buildPersonalizedGreeting(patientContext);

    // Add SOAP-guided assessment
    response += this.buildSOAPGuidedAssessment(request, soapAssessment, contextBlock, []);

    // Add regional health considerations
    if (patientContext?.regionalContext) {
      response += this.addRegionalHealthConsiderations(request.userMessage, patientContext.regionalContext);
    }

    // Add SOAP-guided next steps
    response += this.buildSOAPGuidedNextSteps(soapAssessment, patientContext);

    return response;
  }

  /**
   * Build SOAP-guided assessment section
   */
  private buildSOAPGuidedAssessment(request: AgentRequest, soapAssessment: SOAPAssessment, contextBlock: any, priorityFlags: string[]): string {
    let assessment = '';

    // Handle priority flags
    if (priorityFlags.includes('EMERGENCY_CONSULTATION')) {
      assessment += `🚨 I notice this is marked as urgent. Let me prioritize your immediate concerns. `;
    }

    // SOAP phase-specific responses
    switch (soapAssessment.currentPhase) {
      case 'subjective':
        assessment += `I'm gathering information about your symptoms to provide you with the best care. `;
        if (soapAssessment.completionPercentage < 50) {
          assessment += `We're ${soapAssessment.completionPercentage}% through the initial assessment. `;
        }
        break;
      case 'objective':
        assessment += `Now I'd like to gather some objective information about your condition. `;
        break;
      case 'assessment':
        assessment += `Based on the information you've provided, I'm forming my clinical assessment. `;
        break;
      case 'plan':
        assessment += `Let me outline a comprehensive plan for your care. `;
        break;
    }

    // Reference medical history if relevant
    const medicalHistory = contextBlock.medicalHistory;
    if (medicalHistory && medicalHistory.includes('Chronic Conditions:')) {
      assessment += `I'm reviewing your medical history including your ongoing conditions. `;
    }

    return assessment;
  }

  /**
   * Build SOAP-guided next steps
   */
  private buildSOAPGuidedNextSteps(soapAssessment: SOAPAssessment, patientContext: any): string {
    const nextQuestions = diagnosticFrameworkService.generateNextQuestions(soapAssessment, '');

    if (nextQuestions.length === 0) {
      return '\n\nIs there anything else you\'d like to discuss about your health today?';
    }

    let nextSteps = '\n\nTo provide you with the most appropriate guidance, I need to ask a few more questions:\n';

    nextQuestions.forEach((question, index) => {
      nextSteps += `${index + 1}. ${question}\n`;
    });

    // Add SOAP progress indicator
    nextSteps += `\n📋 Assessment Progress: ${soapAssessment.completionPercentage}% complete`;

    if (soapAssessment.criticalFlags.length > 0) {
      nextSteps += `\n⚠️ Important: ${soapAssessment.criticalFlags.join(', ')}`;
    }

    return nextSteps;
  }

  /**
   * Generate basic structured response when no context is available
   */
  private async generateBasicStructuredResponse(request: AgentRequest, soapAssessment: SOAPAssessment): Promise<string> {
    // Build basic system prompt without rich context
    const basicSystemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

SOAP ASSESSMENT STATUS:
- Current Phase: ${soapAssessment.currentPhase}
- Completion: ${soapAssessment.completionPercentage}%
- Key Findings: ${soapAssessment.keyFindings.join(', ') || 'None recorded'}

INSTRUCTIONS:
1. Follow the SOAP framework for structured assessment
2. Ask relevant follow-up questions based on the current SOAP phase
3. Provide professional, compassionate medical guidance
4. Clearly indicate when specialist referral or emergency care is needed
5. Focus on gathering information needed for proper assessment

Current SOAP Phase Guidance:
- Subjective: Focus on understanding symptoms, history, and patient concerns
- Objective: Gather observable information and recommend appropriate examinations
- Assessment: Provide clinical assessment based on available information
- Plan: Outline treatment recommendations and next steps`;

    // Create conversation messages
    const messages = [
      {
        role: 'system',
        content: basicSystemPrompt
      },
      {
        role: 'user',
        content: request.userMessage
      }
    ];

    // Use AI orchestrator to generate response
    try {
      const aiResponse = await aiOrchestrator.generateResponse({
        messages,
        sessionId: request.sessionId,
        agentType: 'general-practitioner',
        maxTokens: 800,
        temperature: 0.7,
        // Pass available context even for basic responses
        patientContext: request.patientContext,
        urgencyLevel: request.urgencyLevel
      });

      if (aiResponse.success && aiResponse.data?.content) {
        return aiResponse.data.content;
      } else {
        console.warn('AI orchestrator failed, falling back to hardcoded response');
        return this.buildHardcodedBasicResponse(request, soapAssessment);
      }
    } catch (error) {
      console.error('AI orchestrator error, falling back to hardcoded response:', error);
      return this.buildHardcodedBasicResponse(request, soapAssessment);
    }
  }

  /**
   * Build hardcoded basic response as final fallback
   */
  private buildHardcodedBasicResponse(request: AgentRequest, soapAssessment: SOAPAssessment): string {
    let response = `Hello! I'm Dr. Sarah Chen, and I'm here to provide you with comprehensive medical guidance. `;

    // SOAP-guided basic response
    switch (soapAssessment.currentPhase) {
      case 'subjective':
        response += `I'd like to understand your symptoms better using a systematic approach. `;
        break;
      case 'objective':
        response += `Now let's gather some objective information about your condition. `;
        break;
      case 'assessment':
        response += `Based on what you've told me, let me share my clinical assessment. `;
        break;
      case 'plan':
        response += `Let me outline a plan for your care and next steps. `;
        break;
    }

    // Add next questions
    const nextQuestions = diagnosticFrameworkService.generateNextQuestions(soapAssessment, request.userMessage);
    if (nextQuestions.length > 0) {
      response += `\n\nTo provide you with the best guidance, could you help me with:\n`;
      nextQuestions.forEach((question, index) => {
        response += `${index + 1}. ${question}\n`;
      });
    }

    response += `\n📋 Assessment Progress: ${soapAssessment.completionPercentage}% complete`;

    return response;
  }

  /**
   * Generate medical response using comprehensive context and AI orchestrator (legacy method)
   */
  private async generateMedicalResponse(request: AgentRequest): Promise<string> {
    try {
      // Check if we have assembled context for personalized response
      if (request.assembledContext) {
        return await this.generateContextAwareResponse(request);
      }

      // Fallback to basic response if no context available
      return await this.generateBasicResponse(request);

    } catch (error) {
      console.error('❌ Failed to generate medical response:', error);
      return this.generateFallbackResponse();
    }
  }

  /**
   * Generate context-aware response using patient profile and regional data
   */
  private async generateContextAwareResponse(request: AgentRequest): Promise<string> {
    const { assembledContext } = request;
    const { contextBlock, priorityFlags, recommendations } = assembledContext;

    // Use RAG tool to get relevant medical knowledge with geographic context
    const patientContext = request.patientContext;
    const regionalContext = patientContext?.regionalContext;

    let ragResponse = null;
    try {
      ragResponse = await this.ragTool.execute({
        query: request.userMessage,
        parameters: {
          maxResults: 3,
          minRelevanceScore: 0.75,
          documentTypes: ['guideline', 'protocol'],
          specialtyFilter: 'general_medicine',
          regionFilter: regionalContext?.region,
          countryFilter: regionalContext?.countryCode,
          urgencyLevel: request.urgencyLevel || 'medium'
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: request.sessionId,
        agentId: this.id
      });
    } catch (error) {
      console.warn('RAG tool failed, continuing without knowledge retrieval:', error);
    }

    // Build personalized response
    let response = this.buildPersonalizedGreeting(patientContext);

    // Add context-specific assessment
    response += this.buildContextualAssessment(request, contextBlock, priorityFlags);

    // Add regional health considerations
    if (regionalContext) {
      response += this.addRegionalHealthConsiderations(request.userMessage, regionalContext);
    }

    // Add medical knowledge if available
    if (ragResponse?.success && ragResponse.data?.documents?.length > 0) {
      response += this.incorporateRelevantKnowledge(ragResponse.data);
    }

    // Add personalized recommendations
    response += this.buildPersonalizedRecommendations(patientContext, recommendations);

    // Add appropriate follow-up questions
    response += this.generateContextualFollowUp(request, contextBlock);

    return response;
  }

  /**
   * Build personalized greeting using patient context
   */
  private buildPersonalizedGreeting(patientContext: any): string {
    if (!patientContext?.patientProfile) {
      return `Hello! I'm Dr. Sarah Chen, your AI General Practitioner. `;
    }

    const profile = patientContext.patientProfile;
    const timeOfDay = new Date().getHours() < 12 ? 'morning' :
                     new Date().getHours() < 17 ? 'afternoon' : 'evening';

    return `Good ${timeOfDay}! I'm Dr. Sarah Chen, your AI General Practitioner. I see you're calling from ${profile.city || 'your location'}, and I have your medical history available to provide you with personalized care. `;
  }

  /**
   * Build contextual assessment based on patient data
   */
  private buildContextualAssessment(request: AgentRequest, contextBlock: any, priorityFlags: string[]): string {
    let assessment = '';

    // Handle priority flags
    if (priorityFlags.includes('EMERGENCY_CONSULTATION')) {
      assessment += `🚨 I notice this is marked as urgent. Let me prioritize your immediate concerns. `;
    }

    if (priorityFlags.includes('CRITICAL_ALLERGIES')) {
      assessment += `⚠️ I see you have critical allergies on file - I'll keep these in mind during our consultation. `;
    }

    // Reference medical history if relevant
    const medicalHistory = contextBlock.medicalHistory;
    if (medicalHistory && medicalHistory.includes('Chronic Conditions:')) {
      assessment += `I'm reviewing your medical history including your ongoing conditions. `;
    }

    return assessment;
  }

  /**
   * Add regional health considerations
   */
  private addRegionalHealthConsiderations(userMessage: string, regionalContext: any): string {
    if (!regionalContext) return '';

    const message = userMessage.toLowerCase();
    let considerations = '';

    // Check for symptoms that might relate to endemic diseases
    if (message.includes('fever') && regionalContext.endemicDiseases?.includes('Malaria')) {
      considerations += `Given that you're in ${regionalContext.countryName}, I want to consider malaria as a potential cause of fever, especially during ${regionalContext.currentSeason}. `;
    }

    if (message.includes('cough') || message.includes('breathing')) {
      const seasonalRisks = regionalContext.seasonalRisks || [];
      if (seasonalRisks.includes('Respiratory infections')) {
        considerations += `I notice respiratory infections are common during this season in your area. `;
      }
    }

    // Add general seasonal awareness
    if (regionalContext.currentSeason && regionalContext.seasonalRisks?.length > 0) {
      considerations += `It's currently ${regionalContext.currentSeason} in ${regionalContext.countryName}, when we typically see more cases of ${regionalContext.seasonalRisks.slice(0, 2).join(' and ')}. `;
    }

    return considerations;
  }

  /**
   * Incorporate relevant medical knowledge from RAG
   */
  private incorporateRelevantKnowledge(ragData: any): string {
    if (!ragData.documents || ragData.documents.length === 0) return '';

    const topDocument = ragData.documents[0];
    return `Based on current medical guidelines, ${topDocument.content.substring(0, 200)}... `;
  }

  /**
   * Build personalized recommendations
   */
  private buildPersonalizedRecommendations(patientContext: any, recommendations: string[]): string {
    let recs = '\n\nBased on your profile and current situation, I recommend:\n';

    // Add context-specific recommendations
    recommendations.forEach((rec, index) => {
      recs += `${index + 1}. ${rec}\n`;
    });

    // Add healthcare access considerations
    const profile = patientContext?.patientProfile;
    if (profile?.healthcareAccessLevel === 'limited') {
      recs += `${recommendations.length + 1}. Given healthcare access in your area, I'll focus on practical self-care measures you can implement.\n`;
    }

    return recs;
  }

  /**
   * Generate contextual follow-up questions
   */
  private generateContextualFollowUp(request: AgentRequest, contextBlock: any): string {
    const message = request.userMessage.toLowerCase();
    let followUp = '\n\nTo provide you with the most appropriate guidance, could you tell me:\n';

    if (message.includes('pain') || message.includes('hurt')) {
      followUp += '- When did this pain start and how severe is it (1-10 scale)?\n';
      followUp += '- Does anything make it better or worse?\n';
    }

    if (message.includes('fever') || message.includes('temperature')) {
      followUp += '- Have you taken your temperature? If so, what was it?\n';
      followUp += '- Are you experiencing any other symptoms along with the fever?\n';
    }

    // Add medication interaction check if patient has current medications
    if (contextBlock.medicalHistory?.includes('Current Medications:')) {
      followUp += '- Have you taken any new medications or changed your current medications recently?\n';
    }

    followUp += '\nThis information will help me provide you with more specific and safe guidance.';
    return followUp;
  }

  /**
   * Generate basic response when no context is available
   */
  private async generateBasicResponse(request: AgentRequest): Promise<string> {
    const hasSymptoms = request.userMessage.toLowerCase().includes('symptom') ||
                       request.userMessage.toLowerCase().includes('pain') ||
                       request.userMessage.toLowerCase().includes('feel');

    if (hasSymptoms) {
      return `Thank you for sharing your concerns with me. As your primary care provider, I want to help you understand your symptoms and guide you toward appropriate care.

Based on what you've described, I'd like to gather a bit more information to provide you with the best guidance:

1. How long have you been experiencing these symptoms?
2. On a scale of 1-10, how would you rate the severity?
3. Have you noticed any patterns or triggers?
4. Are you currently taking any medications?

While I can provide general guidance and education, it's important that you have a proper medical evaluation with a healthcare provider who can perform a physical examination and review your complete medical history.

In the meantime, please monitor your symptoms and seek immediate medical attention if they worsen or if you develop any concerning new symptoms.

Is there anything specific about your symptoms you'd like me to explain or any general health questions I can help address?`;
    }

    return `Hello! I'm Dr. Sarah Chen, and I'm here to provide you with general health guidance and education.

I can help you with:
- Understanding common health conditions
- General wellness and preventive care advice
- Guidance on when to seek medical attention
- Basic health education and lifestyle recommendations

Please remember that while I can provide general information and guidance, this doesn't replace a proper medical examination with a healthcare provider. For specific medical concerns, diagnosis, or treatment, you should consult with a licensed healthcare professional in your area.

How can I assist you with your health questions today?`;
  }

  /**
   * Generate fallback response for errors
   */
  private generateFallbackResponse(): string {
    return `I apologize, but I'm experiencing some technical difficulties accessing your medical context. For your safety, please consult with a healthcare provider directly if you have urgent medical concerns.

I can still provide general health information if you'd like to rephrase your question.`;
  }

  /**
   * Calculate confidence score for the response
   */
  private calculateConfidence(request: AgentRequest): number {
    let confidence = 0.8; // Base confidence for GP

    // Reduce confidence for complex specialist topics
    const specialistKeywords = ['surgery', 'oncology', 'neurology', 'complex cardiac'];
    if (specialistKeywords.some(keyword => 
      request.userMessage.toLowerCase().includes(keyword)
    )) {
      confidence -= 0.3;
    }

    // Increase confidence for common primary care topics
    const primaryCareKeywords = ['cold', 'flu', 'general health', 'wellness', 'prevention'];
    if (primaryCareKeywords.some(keyword => 
      request.userMessage.toLowerCase().includes(keyword)
    )) {
      confidence += 0.1;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Generate follow-up actions
   */
  private generateFollowUpActions(request: AgentRequest) {
    const actions = [];

    // Always recommend establishing care with a local provider
    actions.push({
      type: 'schedule_appointment' as const,
      description: 'Schedule appointment with local primary care provider',
      timeframe: 'Within 2-4 weeks for routine care',
      priority: 'medium' as const
    });

    // Add specific follow-ups based on content
    if (request.userMessage.toLowerCase().includes('medication')) {
      actions.push({
        type: 'medication_reminder' as const,
        description: 'Review current medications with healthcare provider',
        timeframe: 'Next appointment',
        priority: 'high' as const
      });
    }

    return actions;
  }

  /**
   * Enhanced confidence scoring for GP requests
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = super.getConfidenceScore(request);

    // GP is good for general health questions
    const generalHealthKeywords = [
      'general health', 'wellness', 'prevention', 'routine', 'checkup',
      'primary care', 'family medicine', 'common cold', 'flu'
    ];

    const message = request.userMessage.toLowerCase();
    if (generalHealthKeywords.some(keyword => message.includes(keyword))) {
      score += 0.2;
    }

    // Reduce score for highly specialized topics
    const specializedKeywords = [
      'surgery', 'oncology', 'neurosurgery', 'complex cardiac procedures'
    ];

    if (specializedKeywords.some(keyword => message.includes(keyword))) {
      score -= 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }
}

export default GeneralPractitionerAgent;
